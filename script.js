// Global variables for main features slider
let mainFeaturesCurrentSlide = 0;
let mainFeaturesMaxSlide = 0;

$(document).ready(function() {
    // Mobile menu toggle
    $('.hamburger').on('click', function() {
        $(this).toggleClass('active');
        $('.nav-menu').toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.nav-menu a').on('click', function() {
        $('.hamburger').removeClass('active');
        $('.nav-menu').removeClass('active');
    });

    // Close mobile menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.navbar').length) {
            $('.hamburger').removeClass('active');
            $('.nav-menu').removeClass('active');
        }
    });

    // Initialize main features slider
    initMainFeaturesSlider();

    // Mobile features slider functionality
    let currentSlide = 0;
    const slidesPerView = window.innerWidth <= 480 ? 1 : 2;
    const totalSlides = $('.feature-slide').length;
    const maxSlide = Math.ceil(totalSlides / slidesPerView) - 1;

    function updateFeaturesSlider() {
        const slideWidth = $('.feature-slide').outerWidth(true);
        const translateX = -(currentSlide * slideWidth * slidesPerView);
        $('.features-slider').css('transform', `translateX(${translateX}px)`);

        $('.pagination-dot').removeClass('active');
        $('.pagination-dot').eq(currentSlide).addClass('active');
    }

    // Initialize slider
    updateFeaturesSlider();

    // Touch/swipe functionality for mobile features slider
    let startX = 0;
    let isDragging = false;

    $('.slider-container').on('touchstart mousedown', function(e) {
        startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
        isDragging = true;
    });

    $('.slider-container').on('touchmove mousemove', function(e) {
        if (!isDragging) return;
        e.preventDefault();
    });

    $('.slider-container').on('touchend mouseup', function(e) {
        if (!isDragging) return;
        isDragging = false;

        const endX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;
        const diff = startX - endX;

        if (Math.abs(diff) > 50) {
            if (diff > 0 && currentSlide < maxSlide) {
                currentSlide++;
            } else if (diff < 0 && currentSlide > 0) {
                currentSlide--;
            }
            updateFeaturesSlider();
        }
    });

    // Pagination dot navigation
    $('.pagination-dot').on('click', function() {
        currentSlide = $(this).index();
        updateFeaturesSlider();
    });

    // Auto-slide for mobile features
    setInterval(function() {
        if ($(window).width() <= 768 && $('.mobile-features-slider').is(':visible')) {
            currentSlide = currentSlide >= maxSlide ? 0 : currentSlide + 1;
            updateFeaturesSlider();
        }
    }, 4000);

    // Smooth scrolling for navigation links
    $('.main-feature-item').on('click', function(e) {
        e.preventDefault();
        const target = $(this).attr('href');
        if (target && target !== '#') {
            $('html, body').animate({
                scrollTop: $(target).offset().top - 80
            }, 800);
        }
    });

    // Window resize handler
    $(window).on('resize', function() {
        updateFeaturesSlider();
        initMainFeaturesSlider();
    });

    // Button interactions
    $('.activate-btn').on('click', function() {
        $(this).text('Activated!').css('background', '#28a745');
        setTimeout(() => {
            $(this).text('Activate').css('background', '#4a90e2');
        }, 2000);
    });

    // Video preview interactions
    $('.play-button').on('click', function() {
        alert('Video would play here in a real implementation');
    });

    // CTA button interactions
    $('.cta-btn, .btn-trial').on('click', function() {
        alert('This would redirect to the registration page');
    });

    $('.learn-more-btn').on('click', function(e) {
        e.preventDefault();
        alert('This would redirect to the detailed feature page');
    });

    // Feature link interactions
    $('.feature-link').on('click', function(e) {
        e.preventDefault();
        alert('This would redirect to the specific feature documentation');
    });

    // Watch video button
    $('.watch-video-btn').on('click', function(e) {
        e.preventDefault();
        alert('This would open a video modal or redirect to video page');
    });

    // Demo button
    $('.btn-demo').on('click', function() {
        alert('This would redirect to the demo booking page');
    });

    // Login button
    $('.btn-login').on('click', function() {
        alert('This would redirect to the login page');
    });

    // Touch events for main features slider
    let mainStartX = 0;
    let mainCurrentX = 0;
    let mainIsDragging = false;
    let mainStartTransform = 0;

    $('.main-features-grid').on('touchstart mousedown', function(e) {
        if ($(window).width() <= 768) {
            mainStartX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            mainCurrentX = mainStartX;
            mainIsDragging = true;

            // Get current transform value
            const currentTransform = $(this).css('transform');
            if (currentTransform && currentTransform !== 'none') {
                const matrix = currentTransform.match(/matrix.*\((.+)\)/);
                if (matrix) {
                    mainStartTransform = parseFloat(matrix[1].split(', ')[4]);
                }
            } else {
                mainStartTransform = 0;
            }

            // Disable transition during drag
            $(this).css('transition', 'none');
        }
    });

    $('.main-features-grid').on('touchmove mousemove', function(e) {
        if (!mainIsDragging || $(window).width() > 768) return;
        e.preventDefault();

        mainCurrentX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
        const diff = mainCurrentX - mainStartX;
        const newTransform = mainStartTransform + diff;

        // Apply real-time transform with some resistance at boundaries
        const itemWidth = $('.main-feature-item').outerWidth(true);
        const maxTransform = -(mainFeaturesMaxSlide * itemWidth);

        let constrainedTransform = newTransform;
        if (newTransform > 0) {
            constrainedTransform = newTransform * 0.3; // Resistance when going past left boundary
        } else if (newTransform < maxTransform) {
            constrainedTransform = maxTransform + (newTransform - maxTransform) * 0.3; // Resistance when going past right boundary
        }

        $(this).css('transform', `translateX(${constrainedTransform}px)`);
    });

    $('.main-features-grid').on('touchend mouseup', function() {
        if (!mainIsDragging || $(window).width() > 768) return;
        mainIsDragging = false;

        // Re-enable transition
        $(this).css('transition', 'transform 0.3s ease');

        const totalDiff = mainCurrentX - mainStartX;
        const itemWidth = $('.main-feature-item').outerWidth(true);

        // Calculate how many items to move based on swipe distance and velocity
        if (Math.abs(totalDiff) > 20) {
            const itemsToMove = Math.round(Math.abs(totalDiff) / (itemWidth * 0.7)); // More sensitive

            if (totalDiff < 0 && mainFeaturesCurrentSlide < mainFeaturesMaxSlide) {
                // Swiping left (next)
                mainFeaturesCurrentSlide += Math.min(itemsToMove, mainFeaturesMaxSlide - mainFeaturesCurrentSlide);
            } else if (totalDiff > 0 && mainFeaturesCurrentSlide > 0) {
                // Swiping right (prev)
                mainFeaturesCurrentSlide -= Math.min(itemsToMove, mainFeaturesCurrentSlide);
            }
        }

        // Snap to valid position
        updateMainFeaturesSlider();
    });

    // Add scroll event listener for smooth fade effects
    $('.main-features-grid').on('scroll', function() {
        if ($(window).width() <= 768) {
            updateFadeEffects();
        }
    });
});

// Main features slider functions (global scope for onclick handlers)
function initMainFeaturesSlider() {
    if ($(window).width() <= 768) {
        const totalItems = $('.main-feature-item').length;
        const visibleItems = 3.7; // Show 3.7 items
        mainFeaturesMaxSlide = Math.max(0, totalItems - visibleItems);

        // Reset to first slide if current slide is out of bounds
        if (mainFeaturesCurrentSlide > mainFeaturesMaxSlide) {
            mainFeaturesCurrentSlide = 0;
        }

        // Ensure transition is enabled
        $('.main-features-grid').css('transition', 'transform 0.3s ease');

        updateMainFeaturesSlider();
        updateFadeEffects();
    } else {
        // Remove fade effects on desktop
        $('#mainFeaturesContainer').removeClass('show-left-fade show-right-fade');
        $('.main-features-grid').css({
            'transform': 'none',
            'transition': 'none'
        });
    }
}

function updateMainFeaturesSlider() {
    if ($(window).width() <= 768) {
        const grid = $('.main-features-grid');
        const itemWidth = $('.main-feature-item').outerWidth(true);
        const scrollLeft = mainFeaturesCurrentSlide * itemWidth;

        // Ensure we don't go beyond boundaries
        const maxScroll = mainFeaturesMaxSlide * itemWidth;
        const constrainedScroll = Math.max(0, Math.min(scrollLeft, maxScroll));

        grid.css('transform', `translateX(-${constrainedScroll}px)`);
        updateFadeEffects();
    }
}

function updateFadeEffects() {
    if ($(window).width() <= 768) {
        const container = $('#mainFeaturesContainer');
        const totalItems = $('.main-feature-item').length;

        // Show left fade if not at the beginning
        if (mainFeaturesCurrentSlide > 0) {
            container.addClass('show-left-fade');
        } else {
            container.removeClass('show-left-fade');
        }

        // Show right fade if not at the end (considering we show 3.7 items)
        if (mainFeaturesCurrentSlide < totalItems - 3.7) {
            container.addClass('show-right-fade');
        } else {
            container.removeClass('show-right-fade');
        }
    }
}

function slideMainFeatures(direction) {
    if (direction === 'next' && mainFeaturesCurrentSlide < mainFeaturesMaxSlide) {
        mainFeaturesCurrentSlide++;
    } else if (direction === 'prev' && mainFeaturesCurrentSlide > 0) {
        mainFeaturesCurrentSlide--;
    }
    updateMainFeaturesSlider();
}


// Global variables for main features slider
let mainFeaturesCurrentSlide = 0;
let mainFeaturesMaxSlide = 0;

$(document).ready(function() {
    // Mobile menu toggle
    $('.hamburger').on('click', function() {
        $(this).toggleClass('active');
        $('.nav-menu').toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.nav-menu a').on('click', function() {
        $('.hamburger').removeClass('active');
        $('.nav-menu').removeClass('active');
    });

    // Close mobile menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.navbar').length) {
            $('.hamburger').removeClass('active');
            $('.nav-menu').removeClass('active');
        }
    });

    // Initialize main features slider
    initMainFeaturesSlider();

    // Mobile features slider functionality
    let currentSlide = 0;
    const slidesPerView = window.innerWidth <= 480 ? 1 : 2;
    const totalSlides = $('.feature-slide').length;
    const maxSlide = Math.ceil(totalSlides / slidesPerView) - 1;

    function updateFeaturesSlider() {
        const slideWidth = $('.feature-slide').outerWidth(true);
        const translateX = -(currentSlide * slideWidth * slidesPerView);
        $('.features-slider').css('transform', `translateX(${translateX}px)`);

        $('.pagination-dot').removeClass('active');
        $('.pagination-dot').eq(currentSlide).addClass('active');
    }

    // Initialize slider
    updateFeaturesSlider();

    // Touch/swipe functionality for mobile features slider
    let startX = 0;
    let isDragging = false;

    $('.slider-container').on('touchstart mousedown', function(e) {
        startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
        isDragging = true;
    });

    $('.slider-container').on('touchmove mousemove', function(e) {
        if (!isDragging) return;
        e.preventDefault();
    });

    $('.slider-container').on('touchend mouseup', function(e) {
        if (!isDragging) return;
        isDragging = false;

        const endX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;
        const diff = startX - endX;

        if (Math.abs(diff) > 50) {
            if (diff > 0 && currentSlide < maxSlide) {
                currentSlide++;
            } else if (diff < 0 && currentSlide > 0) {
                currentSlide--;
            }
            updateFeaturesSlider();
        }
    });

    // Pagination dot navigation
    $('.pagination-dot').on('click', function() {
        currentSlide = $(this).index();
        updateFeaturesSlider();
    });

    // Auto-slide for mobile features
    setInterval(function() {
        if ($(window).width() <= 768 && $('.mobile-features-slider').is(':visible')) {
            currentSlide = currentSlide >= maxSlide ? 0 : currentSlide + 1;
            updateFeaturesSlider();
        }
    }, 4000);

    // Smooth scrolling for navigation links
    $('.main-feature-item').on('click', function(e) {
        e.preventDefault();
        const target = $(this).attr('href');
        if (target && target !== '#') {
            $('html, body').animate({
                scrollTop: $(target).offset().top - 80
            }, 800);
        }
    });

    // Window resize handler
    $(window).on('resize', function() {
        updateFeaturesSlider();
        initMainFeaturesSlider();
    });

    // Button interactions
    $('.activate-btn').on('click', function() {
        $(this).text('Activated!').css('background', '#28a745');
        setTimeout(() => {
            $(this).text('Activate').css('background', '#4a90e2');
        }, 2000);
    });

    // Video preview interactions
    $('.play-button').on('click', function() {
        alert('Video would play here in a real implementation');
    });

    // CTA button interactions
    $('.cta-btn, .btn-trial').on('click', function() {
        alert('This would redirect to the registration page');
    });

    $('.learn-more-btn').on('click', function(e) {
        e.preventDefault();
        alert('This would redirect to the detailed feature page');
    });

    // Feature link interactions
    $('.feature-link').on('click', function(e) {
        e.preventDefault();
        alert('This would redirect to the specific feature documentation');
    });

    // Watch video button
    $('.watch-video-btn').on('click', function(e) {
        e.preventDefault();
        alert('This would open a video modal or redirect to video page');
    });

    // Demo button
    $('.btn-demo').on('click', function() {
        alert('This would redirect to the demo booking page');
    });

    // Login button
    $('.btn-login').on('click', function() {
        alert('This would redirect to the login page');
    });

    // Main features pagination dots
    $('.main-features-dot').on('click', function() {
        mainFeaturesCurrentSlide = parseInt($(this).data('slide'));
        updateMainFeaturesSlider();
    });

    // Touch events for main features slider
    let mainStartX = 0;
    let mainIsDragging = false;

    $('.main-features-grid').on('touchstart mousedown', function(e) {
        if ($(window).width() <= 768) {
            mainStartX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            mainIsDragging = true;
        }
    });

    $('.main-features-grid').on('touchmove mousemove', function(e) {
        if (!mainIsDragging || $(window).width() > 768) return;
        e.preventDefault();
    });

    $('.main-features-grid').on('touchend mouseup', function(e) {
        if (!mainIsDragging || $(window).width() > 768) return;
        mainIsDragging = false;

        const endX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;
        const diff = mainStartX - endX;

        if (Math.abs(diff) > 50) {
            if (diff > 0 && mainFeaturesCurrentSlide < mainFeaturesMaxSlide) {
                mainFeaturesCurrentSlide++;
            } else if (diff < 0 && mainFeaturesCurrentSlide > 0) {
                mainFeaturesCurrentSlide--;
            }
            updateMainFeaturesSlider();
        }
    });
});

// Main features slider functions (global scope for onclick handlers)
function initMainFeaturesSlider() {
    if ($(window).width() <= 768) {
        const itemsPerSlide = $(window).width() <= 480 ? 1 : 2;
        const totalItems = $('.main-feature-item').length;
        mainFeaturesMaxSlide = Math.max(0, Math.ceil(totalItems / itemsPerSlide) - 1);

        // Reset to first slide if current slide is out of bounds
        if (mainFeaturesCurrentSlide > mainFeaturesMaxSlide) {
            mainFeaturesCurrentSlide = 0;
        }

        updateMainFeaturesSlider();
    }
}

function updateMainFeaturesSlider() {
    if ($(window).width() <= 768) {
        const itemWidth = $('.main-feature-item').outerWidth(true);
        const itemsPerSlide = $(window).width() <= 480 ? 1 : 2;
        const translateX = -(mainFeaturesCurrentSlide * itemWidth * itemsPerSlide);

        $('.main-features-grid').css('transform', `translateX(${translateX}px)`);

        $('.main-features-dot').removeClass('active');
        $('.main-features-dot').eq(mainFeaturesCurrentSlide).addClass('active');
    }
}

function slideMainFeatures(direction) {
    if (direction === 'next' && mainFeaturesCurrentSlide < mainFeaturesMaxSlide) {
        mainFeaturesCurrentSlide++;
    } else if (direction === 'prev' && mainFeaturesCurrentSlide > 0) {
        mainFeaturesCurrentSlide--;
    }
    updateMainFeaturesSlider();
}


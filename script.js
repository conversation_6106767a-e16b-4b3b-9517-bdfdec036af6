$(document).ready(function() {
    // Mobile menu toggle
    $('.hamburger').on('click', function() {
        $(this).toggleClass('active');
        $('.nav-menu').toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.nav-menu a').on('click', function() {
        $('.hamburger').removeClass('active');
        $('.nav-menu').removeClass('active');
    });

    // Close mobile menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.navbar').length) {
            $('.hamburger').removeClass('active');
            $('.nav-menu').removeClass('active');
        }
    });

    // Mobile slider functionality
    let currentSlide = 0;
    const slidesPerView = 2;
    const totalSlides = $('.slider-item').length;
    const maxSlide = Math.ceil(totalSlides / slidesPerView) - 1;

    function updateSlider() {
        const translateX = -(currentSlide * (100 / slidesPerView));
        $('.feature-slider').css('transform', `translateX(${translateX}%)`);
        
        $('.dot').removeClass('active');
        $(`.dot[data-slide="${currentSlide}"]`).addClass('active');
    }

    // Touch/swipe functionality for mobile slider
    let startX = 0;
    let isDragging = false;

    $('.slider-wrapper').on('touchstart mousedown', function(e) {
        startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
        isDragging = true;
    });

    $('.slider-wrapper').on('touchmove mousemove', function(e) {
        if (!isDragging) return;
        e.preventDefault();
    });

    $('.slider-wrapper').on('touchend mouseup', function(e) {
        if (!isDragging) return;
        isDragging = false;
        
        const endX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;
        const diff = startX - endX;
        
        if (Math.abs(diff) > 50) {
            if (diff > 0 && currentSlide < maxSlide) {
                currentSlide++;
            } else if (diff < 0 && currentSlide > 0) {
                currentSlide--;
            }
            updateSlider();
        }
    });

    // Dot navigation
    $('.dot').on('click', function() {
        currentSlide = parseInt($(this).data('slide'));
        updateSlider();
    });

    // Auto-slide (optional)
    setInterval(function() {
        if ($(window).width() <= 768) {
            currentSlide = currentSlide >= maxSlide ? 0 : currentSlide + 1;
            updateSlider();
        }
    }, 5000);

    // Feature card click handlers
    const featureDetails = {
        marketing: {
            content: `
                <h3>Marketing Tools</h3>
                <p>Powerful marketing features include:</p>
                <ul>
                    <li>Advanced analytics and tracking</li>
                    <li>Lead generation forms</li>
                    <li>Social media integration</li>
                    <li>Email marketing campaigns</li>
                </ul>
            `
        },
        sales: {
            content: `
                <h3>Sales Tools</h3>
                <p>Convert readers with:</p>
                <ul>
                    <li>Interactive product catalogs</li>
                    <li>Shopping cart integration</li>
                    <li>Contact forms and CTAs</li>
                    <li>Lead tracking and management</li>
                </ul>
            `
        },
        branding: {
            content: `
                <h3>Branding & Design</h3>
                <p>Customize your publications:</p>
                <ul>
                    <li>Custom colors and fonts</li>
                    <li>Logo and branding elements</li>
                    <li>Custom domain hosting</li>
                    <li>White-label solutions</li>
                </ul>
            `
        },
        management: {
            content: `
                <h3>Management & Security</h3>
                <p>Secure and manage content:</p>
                <ul>
                    <li>Password protection</li>
                    <li>User access controls</li>
                    <li>Content expiration dates</li>
                    <li>Download restrictions</li>
                </ul>
            `
        },
        professional: {
            content: `
                <h3>Professional Format</h3>
                <p>Create professional publications:</p>
                <ul>
                    <li>High-quality PDF conversion</li>
                    <li>Professional layouts and templates</li>
                    <li>Print-ready formatting</li>
                    <li>Multi-format export options</li>
                </ul>
            `
        }
    };

    // Main feature items and feature cards click handlers
    $('.main-feature-item, .feature-card').on('click', function() {
        const featureType = $(this).data('feature');
        const details = featureDetails[featureType];

        if (details) {
            $('#modal-body').html(details.content);
            $('#feature-modal').fadeIn(300);
        }
    });

    // Close modal
    $('.close, .modal').on('click', function(e) {
        if (e.target === this) {
            $('#feature-modal').fadeOut(300);
        }
    });
});


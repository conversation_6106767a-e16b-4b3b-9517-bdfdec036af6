* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #4a90e2;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    height: 32px;
    fill: white;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-menu a {
    text-decoration: none;
    color: white;
    font-weight: 500;
    font-size: 15px;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.nav-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.btn-demo, .btn-trial, .btn-login {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-demo {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-trial {
    background: white;
    color: #4a90e2;
    font-weight: 600;
}

.btn-login {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.btn-demo:hover {
    background: rgba(255, 255, 255, 0.25);
}

.btn-trial:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.btn-login:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    z-index: 1001;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: #4a90e2;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-menu a {
        font-size: 1.2rem;
        padding: 1rem;
        display: block;
        width: 100%;
        text-align: center;
    }
}

/* Hero Section */
.hero {
    background: #4a90e2;
    color: white;
    padding: 120px 0 60px;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 400;
    letter-spacing: -0.02em;
}

.hero p {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
    opacity: 0.95;
}

/* Main Features Section */
.main-features-section {
    padding: 50px 0;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    overflow: hidden;
}

.main-features-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.main-features-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    transition: transform 0.3s ease;
}

.main-feature-item {
    text-align: center;
    padding: 2rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    text-decoration: none;
    color: white;
    display: block;
    min-width: 200px;
}

.main-feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
    color: white;
}

.main-feature-icon {
    margin-bottom: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
}

.main-feature-icon svg {
    width: 40px;
    height: 40px;
}

.main-feature-item h3 {
    font-size: 0.95rem;
    font-weight: 500;
    color: white;
    margin: 0;
    line-height: 1.3;
}

/* Gradient overlays for edge fade effect */
.main-features-container::before,
.main-features-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 40px;
    z-index: 10;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.main-features-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(74, 144, 226, 0.8), transparent);
}

.main-features-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(74, 144, 226, 0.8), transparent);
}

.main-features-container.show-left-fade::before {
    opacity: 1;
}

.main-features-container.show-right-fade::after {
    opacity: 1;
}

/* Feature Detail Sections */
.feature-detail-section {
    padding: 80px 0;
    background: white;
}

.feature-detail-section:nth-child(even) {
    background: #f8f9fa;
}

.feature-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 60px;
}

.feature-header h2 {
    font-size: 2.5rem;
    font-weight: 400;
    color: #333;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
}

.feature-header p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.learn-more-btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    background: #4a90e2;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;
}

.learn-more-btn:hover {
    background: #357abd;
    transform: translateY(-2px);
}

.feature-content {
    max-width: 1000px;
    margin: 0 auto;
}

.feature-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
}

.feature-item.reverse {
    direction: rtl;
}

.feature-item.reverse > * {
    direction: ltr;
}

.feature-text h3 {
    font-size: 1.8rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-text p {
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-link {
    color: #4a90e2;
    text-decoration: none;
    font-weight: 500;
}

.feature-link:hover {
    text-decoration: underline;
}

.feature-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.demo-container {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    border: 2px dashed #ddd;
}

.activate-btn {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 6px;
    font-weight: 500;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.activate-btn:hover {
    background: #357abd;
}

.view-example-btn {
    display: block;
    color: #4a90e2;
    text-decoration: none;
    font-weight: 500;
}

.video-preview {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.7);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
}

.sub-features {
    margin-top: 2rem;
}

.sub-feature {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.sub-feature-icon {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.sub-feature h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.sub-feature p {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.5;
}

.cta-section {
    text-align: center;
    margin-top: 4rem;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.cta-section h3 {
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 1.5rem;
}

.cta-btn {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.cta-btn:hover {
    background: #357abd;
    transform: translateY(-2px);
}

.watch-video-btn {
    display: inline-block;
    color: #4a90e2;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border: 2px solid #4a90e2;
    border-radius: 6px;
    transition: all 0.3s;
}

.watch-video-btn:hover {
    background: #4a90e2;
    color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-top: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem 1rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.5;
    font-size: 0.95rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card.animate {
    animation: fadeInUp 0.6s ease forwards;
}

/* Mobile Features Slider */
.mobile-features-slider {
    padding: 60px 0;
    background: #f8f9fa;
    display: none;
}

.mobile-features-slider h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 400;
    color: #333;
    margin-bottom: 3rem;
}

.slider-container {
    overflow: hidden;
    margin-bottom: 2rem;
}

.features-slider {
    display: flex;
    transition: transform 0.3s ease;
    gap: 1rem;
    padding: 0 1rem;
}

.feature-slide {
    min-width: 280px;
    background: white;
    padding: 2rem 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    flex-shrink: 0;
    border: 1px solid #eee;
}

.slide-icon {
    font-size: 2.5rem;
    margin-bottom: 1.2rem;
    display: block;
}

.feature-slide h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
    line-height: 1.3;
}

.feature-slide p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

.slider-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s;
}

.pagination-dot.active {
    background: #4a90e2;
    transform: scale(1.2);
}

/* Tablet and Mobile Responsive */
@media (max-width: 1024px) {
    .feature-item {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .feature-item.reverse {
        direction: ltr;
    }
}

@media (max-width: 768px) {
    .mobile-features-slider {
        display: block;
    }

    .navbar {
        padding: 1rem;
    }

    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    /* Mobile horizontal scrolling for main features */
    .main-features-container {
        overflow: hidden;
        padding: 0 20px;
    }

    .main-features-grid {
        display: flex;
        grid-template-columns: none;
        gap: 0.75rem;
        transition: transform 0.3s ease;
        width: max-content;
    }

    .main-feature-item {
        flex: 0 0 calc((100vw - 40px - 2.25rem) / 3.7);
        padding: 1.5rem 0.75rem;
        min-width: calc((100vw - 40px - 2.25rem) / 3.7);
        max-width: calc((100vw - 40px - 2.25rem) / 3.7);
    }

    .main-feature-icon svg {
        width: 48px;
        height: 48px;
    }

    .main-feature-item h3 {
        font-size: 1rem;
    }

    /* Enable gradient fade effects on mobile */
    .main-features-container::before,
    .main-features-container::after {
        width: 30px;
    }

    .feature-header h2 {
        font-size: 2rem;
    }

    .feature-header p {
        font-size: 1rem;
    }

    .feature-text h3 {
        font-size: 1.5rem;
    }

    .feature-item {
        margin-bottom: 3rem;
    }

    .cta-section {
        padding: 2rem 1rem;
    }

    .nav-actions {
        gap: 0.5rem;
    }

    .btn-demo, .btn-trial, .btn-login {
        padding: 0.5rem 1rem;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 40px;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .main-features-container {
        padding: 0 15px;
    }

    .main-feature-item {
        flex: 0 0 calc((100vw - 30px - 2.25rem) / 3.7);
        min-width: calc((100vw - 30px - 2.25rem) / 3.7);
        max-width: calc((100vw - 30px - 2.25rem) / 3.7);
        padding: 1.5rem 0.5rem;
    }

    .main-feature-item h3 {
        font-size: 0.95rem;
    }

    .main-feature-icon svg {
        width: 40px;
        height: 40px;
    }

    .feature-slide {
        min-width: 250px;
        padding: 1.5rem 1rem;
    }

    .feature-header h2 {
        font-size: 1.75rem;
    }

    .feature-text h3 {
        font-size: 1.3rem;
    }

    .nav-actions .btn-demo {
        display: none;
    }

    .container {
        padding: 0 15px;
    }
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #4a90e2;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    height: 40px;
    filter: brightness(0) invert(1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: white;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #e0e0e0;
}

.nav-actions {
    display: flex;
    gap: 1rem;
}

.btn-trial, .btn-login {
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-trial {
    background: white;
    color: #4a90e2;
}

.btn-login {
    background: transparent;
    color: white;
    border: 2px solid white;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Hero Section */
.hero {
    background: #4a90e2;
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 300;
}

.hero p {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Features Section */
.features-section {
    padding: 80px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-top: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem 1rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.5;
    font-size: 0.95rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card.animate {
    animation: fadeInUp 0.6s ease forwards;
}

/* Mobile Slider Section */
.mobile-slider-section {
    padding: 60px 0;
    background: #f8f9fa;
    display: none;
}

.slider-wrapper {
    overflow: hidden;
    margin-bottom: 2rem;
}

.feature-slider {
    display: flex;
    transition: transform 0.3s ease;
    gap: 1rem;
}

.slider-item {
    min-width: 280px;
    background: white;
    padding: 2rem 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.slider-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.slider-item h4 {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
    color: #333;
    font-weight: 600;
}

.slider-item p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.slider-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ccc;
    cursor: pointer;
    transition: background 0.3s;
}

.dot.active {
    background: #4a90e2;
}

/* Mobile Responsive Updates */
@media (max-width: 768px) {
    .mobile-slider-section {
        display: block;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem 1rem;
    }
    
    .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .feature-card h3 {
        font-size: 1.1rem;
    }
    
    .feature-card p {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .slider-item {
        min-width: 250px;
        padding: 1.5rem 1rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

